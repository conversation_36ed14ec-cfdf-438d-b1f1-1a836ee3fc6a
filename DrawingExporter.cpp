#include "sierrachart.h"

// Forward declarations
void ExportDrawingsToCSV(SCStudyInterfaceRef& sc);
void ExportUserDrawings(SCStudyInterfaceRef& sc, int FileHandle);
void ExportSingleDrawing(SCStudyInterfaceRef& sc, int FileHandle, const s_UseTool& Drawing);
SCString GetDrawingTypeName(DrawingTypeEnum DrawingType);

/*==========================================================================*/
SCSFExport scsf_DrawingExporter(SCStudyInterfaceRef sc)
{
	// Input references
	SCInputRef Input_ExportOnUpdate = sc.Input[0];
	SCInputRef Input_OutputPath = sc.Input[1];
	SCInputRef Input_IncludeRectangles = sc.Input[2];
	SCInputRef Input_IncludeLines = sc.Input[3];
	SCInputRef Input_IncludeText = sc.Input[4];
	SCInputRef Input_ExportButton = sc.Input[5];

	if (sc.SetDefaults)
	{
		// Set the configuration and defaults
		sc.GraphName = "Drawing Exporter";
		sc.StudyDescription = "Exports chart drawings to CSV file with prices and colors";
		sc.AutoLoop = 0;
		sc.GraphRegion = 0;
		sc.CalculationPrecedence = LOW_PREC_LEVEL;

		// Input settings
		Input_ExportOnUpdate.Name = "Export on Update";
		Input_ExportOnUpdate.SetYesNo(0);

		Input_OutputPath.Name = "Output File Path";
		Input_OutputPath.SetString("C:\\Temp\\drawings_export.csv");

		Input_IncludeRectangles.Name = "Include Rectangle Highlights";
		Input_IncludeRectangles.SetYesNo(1);

		Input_IncludeLines.Name = "Include Lines";
		Input_IncludeLines.SetYesNo(1);

		Input_IncludeText.Name = "Include Text";
		Input_IncludeText.SetYesNo(1);

		Input_ExportButton.Name = "Export Now";
		Input_ExportButton.SetCustomInputStrings("Export;");
		Input_ExportButton.SetCustomInputIndex(0);

		return;
	}

	// Check if export button was pressed
	bool ExportTriggered = false;
	if (Input_ExportButton.GetIndex() == 0)
	{
		ExportTriggered = true;
		Input_ExportButton.SetCustomInputIndex(-1); // Reset button
	}

	// Only export when requested, button pressed, or on last bar update
	if (!ExportTriggered && !Input_ExportOnUpdate.GetYesNo() && sc.Index != sc.ArraySize - 1)
		return;

	// Export drawings to CSV
	ExportDrawingsToCSV(sc);
}

/*==========================================================================*/
void ExportDrawingsToCSV(SCStudyInterfaceRef& sc)
{
	SCString OutputPath = sc.Input[1].GetString();
	
	// Open file for writing
	int FileHandle = 0;
	int32_t Result = sc.OpenFile(OutputPath,
		n_ACSIL::FILE_MODE_OPEN_TO_REWRITE_FROM_START,
		FileHandle);

	if (Result != 1)
	{
		SCString Message;
		Message.Format("Failed to open file: %s", OutputPath.GetChars());
		sc.AddMessageToLog(Message, 1);
		return;
	}

	// Write CSV header
	SCString Header = "DrawingType,LineNumber,BeginDateTime,EndDateTime,BeginValue,EndValue,Color_RGB,Color_Red,Color_Green,Color_Blue,Text\n";
	unsigned int BytesWritten = 0;
	sc.WriteFile(FileHandle, Header.GetChars(), Header.GetLength(), &BytesWritten);

	// Get all user drawings and export them
	ExportUserDrawings(sc, FileHandle);

	// Close file
	sc.CloseFile(FileHandle);

	SCString Message;
	Message.Format("Drawings exported to: %s", OutputPath.GetChars());
	sc.AddMessageToLog(Message, 0);
}

/*==========================================================================*/
void ExportUserDrawings(SCStudyInterfaceRef& sc, int FileHandle)
{
	// Drawing types to export based on user settings
	std::vector<DrawingTypeEnum> DrawingTypesToExport;
	
	if (sc.Input[2].GetYesNo()) // Include rectangles
	{
		DrawingTypesToExport.push_back(DRAWING_RECTANGLEHIGHLIGHT);
		DrawingTypesToExport.push_back(DRAWING_RECTANGLE_EXT_HIGHLIGHT);
	}
	
	if (sc.Input[3].GetYesNo()) // Include lines
	{
		DrawingTypesToExport.push_back(DRAWING_LINE);
		DrawingTypesToExport.push_back(DRAWING_RAY);
		DrawingTypesToExport.push_back(DRAWING_HORIZONTALLINE);
		DrawingTypesToExport.push_back(DRAWING_VERTICALLINE);
		DrawingTypesToExport.push_back(DRAWING_EXTENDED_LINE);
	}
	
	if (sc.Input[4].GetYesNo()) // Include text
	{
		DrawingTypesToExport.push_back(DRAWING_TEXT);
		DrawingTypesToExport.push_back(DRAWING_STATIONARY_TEXT);
	}

	// Iterate through each drawing type
	for (size_t TypeIndex = 0; TypeIndex < DrawingTypesToExport.size(); TypeIndex++)
	{
		DrawingTypeEnum CurrentType = DrawingTypesToExport[TypeIndex];
		
		// Get drawings of this type
		int DrawingIndex = 0;
		s_UseTool Drawing;
		
		while (sc.GetUserDrawnChartDrawing(sc.ChartNumber, CurrentType, Drawing, DrawingIndex) > 0)
		{
			ExportSingleDrawing(sc, FileHandle, Drawing);
			DrawingIndex++;
		}
	}
}

/*==========================================================================*/
void ExportSingleDrawing(SCStudyInterfaceRef& sc, int FileHandle, const s_UseTool& Drawing)
{
	SCString Line;

	// Drawing type name
	SCString DrawingTypeName = GetDrawingTypeName(Drawing.DrawingType);

	// Format date times
	SCString BeginDateTimeStr = sc.FormatDateTime(Drawing.BeginDateTime);
	SCString EndDateTimeStr = sc.FormatDateTime(Drawing.EndDateTime);

	// Extract RGB color components
	uint32_t Color = Drawing.Color;
	int Red = (Color & 0xFF);
	int Green = ((Color >> 8) & 0xFF);
	int Blue = ((Color >> 16) & 0xFF);

	// Format RGB as hex string
	SCString ColorRGB;
	ColorRGB.Format("#%02X%02X%02X", Red, Green, Blue);

	// Escape text for CSV (replace quotes and commas)
	SCString EscapedText = Drawing.Text;
	EscapedText.Replace("\"", "\"\"");
	if (EscapedText.IndexOf(',') >= 0 || EscapedText.IndexOf('"') >= 0)
	{
		EscapedText = "\"" + EscapedText + "\"";
	}

	// Build CSV line
	Line.Format("%s,%d,%s,%s,%.6f,%.6f,%s,%d,%d,%d,%s\n",
		DrawingTypeName.GetChars(),
		Drawing.LineNumber,
		BeginDateTimeStr.GetChars(),
		EndDateTimeStr.GetChars(),
		Drawing.BeginValue,
		Drawing.EndValue,
		ColorRGB.GetChars(),
		Red,
		Green,
		Blue,
		EscapedText.GetChars());

	// Write to file
	unsigned int BytesWritten = 0;
	sc.WriteFile(FileHandle, Line.GetChars(), Line.GetLength(), &BytesWritten);
}

/*==========================================================================*/
SCString GetDrawingTypeName(DrawingTypeEnum DrawingType)
{
	switch (DrawingType)
	{
		case DRAWING_LINE: return "Line";
		case DRAWING_RAY: return "Ray";
		case DRAWING_HORIZONTALLINE: return "Horizontal Line";
		case DRAWING_VERTICALLINE: return "Vertical Line";
		case DRAWING_ARROW: return "Arrow";
		case DRAWING_TEXT: return "Text";
		case DRAWING_RECTANGLEHIGHLIGHT: return "Rectangle Highlight";
		case DRAWING_ELLIPSEHIGHLIGHT: return "Ellipse Highlight";
		case DRAWING_RECTANGLE_EXT_HIGHLIGHT: return "Extended Rectangle Highlight";
		case DRAWING_EXTENDED_LINE: return "Extended Line";
		case DRAWING_STATIONARY_TEXT: return "Stationary Text";
		case DRAWING_RETRACEMENT: return "Retracement";
		case DRAWING_PITCHFORK: return "Pitchfork";
		case DRAWING_PARALLEL_LINES: return "Parallel Lines";
		case DRAWING_PARALLEL_RAYS: return "Parallel Rays";
		case DRAWING_TRIANGLE: return "Triangle";
		case DRAWING_ANGLED_ELLIPSE: return "Angled Ellipse";
		default: return "Unknown";
	}
}
